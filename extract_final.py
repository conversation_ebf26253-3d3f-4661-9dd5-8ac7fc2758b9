#!/usr/bin/env python3
import re
import html

def extract_individual_links(content):
    """Extract individual links from a section"""
    # Pattern to match individual link items
    link_pattern = r'<div class="link-(?:item|card)">\s*<a href="([^"]+)"[^>]*>([^<]*(?:<[^>]*>[^<]*)*)</a>\s*</div>'
    
    links = []
    for match in re.finditer(link_pattern, content, re.DOTALL):
        url = match.group(1)
        title_html = match.group(2)
        
        # Clean up title - remove HTML tags and normalize whitespace
        title = re.sub(r'<[^>]*>', '', title_html)
        title = html.unescape(title)
        title = re.sub(r'\s+', ' ', title).strip()
        
        if title and url:
            links.append({
                'title': title,
                'url': url
            })
    
    return links

def extract_sections_detailed(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split content into sections
    sections = {}
    
    # Find all section markers
    section_markers = []
    
    # Main section (academicPortal)
    main_match = re.search(r'<div id="academicPortal" class="section-card">', content)
    if main_match:
        section_markers.append(('academicPortal', main_match.start()))
    
    # Regular sections
    for match in re.finditer(r'<div id="([^"]+)" class="section">', content):
        section_markers.append((match.group(1), match.start()))
    
    # Sort by position
    section_markers.sort(key=lambda x: x[1])
    
    # Extract each section
    for i, (section_id, start_pos) in enumerate(section_markers):
        # Find end position
        if i + 1 < len(section_markers):
            end_pos = section_markers[i + 1][1]
        else:
            end_pos = len(content)
        
        section_content = content[start_pos:end_pos]
        
        # Extract title
        title_match = re.search(r'<h2[^>]*>([^<]*(?:<[^>]*>[^<]*)*)</h2>', section_content)
        if title_match:
            title = re.sub(r'<[^>]*>', '', title_match.group(1)).strip()
            title = html.unescape(title)
        else:
            # Try to get title from section navigation
            nav_titles = {
                'academicPortal': '学术门户',
                'paperSearch': '论文搜索与下载',
                'proteinSites': '蛋白质网站',
                'bookDownload': '书籍与论文下载网站',
                'translation': '翻译网站',
                'importantJournals': '重要论文网址',
                'publicAccounts': '优质公众号',
                'frontendDev': '前端开发网站',
                'githubRepos': '优质github仓库',
                'molecularDynamics': '分子动力学模拟',
                'bilibiliDownload': 'Bilibili视频下载网站',
                'patentDownload': '专利下载软件',
                'databases': '常见数据库汇总',
                'programmingEssentials': '编程必备',
                'molecularBiology': '分子生物学工具',
                'llmTools': '大模型工具',
                'itTools': 'IT工具',
                'mcpTools': 'MCP相关',
                'proteinModels': '蛋白质大模型',
                'aiAgents': 'AI智能体'
            }
            title = nav_titles.get(section_id, section_id)
        
        # Extract links
        links = extract_individual_links(section_content)
        
        sections[section_id] = {
            'title': title,
            'links': links
        }
    
    return sections

# Extract sections
sections = extract_sections_detailed('/home/<USER>/桌面/sample/daohang.html')

# Display results
print("# Complete Structure Analysis of daohang.html")
print("=" * 80)

total_links = 0
for section_id, data in sections.items():
    print(f"\n## Section: {section_id}")
    print(f"**Title:** {data['title']}")
    print(f"**Number of Links:** {len(data['links'])}")
    total_links += len(data['links'])
    
    if data['links']:
        print("**Links:**")
        for i, link in enumerate(data['links'], 1):
            print(f"  {i}. {link['title']}")
            print(f"     URL: {link['url']}")
    else:
        print("**Links:** None")
    
    print("-" * 60)

print(f"\n## Summary")
print(f"Total Sections: {len(sections)}")
print(f"Total Links: {total_links}")

# Save detailed JSON
import json
with open('/home/<USER>/桌面/sample/detailed_structure.json', 'w', encoding='utf-8') as f:
    json.dump(sections, f, ensure_ascii=False, indent=2)

print(f"\nDetailed structure saved to: /home/<USER>/桌面/sample/detailed_structure.json")