#!/usr/bin/env python3
import re
import html
import json

def extract_sections_correctly(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find all sections
    sections = {}
    
    # Pattern to find sections
    section_pattern = r'<div id="([^"]+)" class="(section-card|section)"[^>]*>'
    
    # Find all section matches
    section_matches = list(re.finditer(section_pattern, content))
    
    for i, match in enumerate(section_matches):
        section_id = match.group(1)
        section_start = match.start()
        
        # Find the end of this section (start of next section or end of content)
        if i + 1 < len(section_matches):
            section_end = section_matches[i + 1].start()
        else:
            section_end = len(content)
        
        section_content = content[section_start:section_end]
        
        # Extract section title
        title_pattern = r'<h2[^>]*class="[^"]*"[^>]*>([^<]*(?:<[^>]*>[^<]*)*)</h2>'
        title_match = re.search(title_pattern, section_content)
        
        if title_match:
            title = re.sub(r'<[^>]*>', '', title_match.group(1)).strip()
            title = html.unescape(title)
        else:
            # Fallback titles from navigation
            nav_titles = {
                'academicPortal': '学术门户',
                'paperSearch': '论文搜索与下载',
                'proteinSites': '蛋白质网站',
                'bookDownload': '书籍与论文下载网站',
                'translation': '翻译网站',
                'importantJournals': '重要论文网址',
                'publicAccounts': '优质公众号',
                'frontendDev': '前端开发网站',
                'githubRepos': '优质github仓库',
                'molecularDynamics': '分子动力学模拟',
                'bilibiliDownload': 'Bilibili视频下载网站',
                'patentDownload': '专利下载软件',
                'databases': '常见数据库汇总',
                'programmingEssentials': '编程必备',
                'molecularBiology': '分子生物学工具',
                'llmTools': '大模型工具',
                'itTools': 'IT工具',
                'mcpTools': 'MCP相关',
                'proteinModels': '蛋白质大模型',
                'aiAgents': 'AI智能体'
            }
            title = nav_titles.get(section_id, section_id)
        
        # Extract individual links
        links = []
        
        # Pattern for link-item and link-card structures
        link_patterns = [
            r'<div class="link-item">\s*<a href="([^"]+)"[^>]*>([^<]*(?:<[^>]*>[^<]*)*)</a>\s*</div>',
            r'<div class="link-card">\s*<a href="([^"]+)"[^>]*>([^<]*(?:<[^>]*>[^<]*)*)</a>\s*</div>'
        ]
        
        for pattern in link_patterns:
            for match in re.finditer(pattern, section_content, re.DOTALL):
                url = match.group(1)
                title_text = match.group(2)
                
                # Clean up title
                clean_title = re.sub(r'<[^>]*>', '', title_text)
                clean_title = html.unescape(clean_title)
                clean_title = re.sub(r'\s+', ' ', clean_title).strip()
                
                if clean_title and url:
                    links.append({
                        'title': clean_title,
                        'url': url
                    })
        
        sections[section_id] = {
            'title': title,
            'links': links
        }
    
    return sections

# Extract sections
sections = extract_sections_correctly('/home/<USER>/桌面/sample/daohang.html')

# Display results
print("# Complete Structure Analysis of daohang.html")
print("=" * 80)

total_links = 0
for section_id, data in sections.items():
    print(f"\n## Section: {section_id}")
    print(f"**Title:** {data['title']}")
    print(f"**Number of Links:** {len(data['links'])}")
    total_links += len(data['links'])
    
    if data['links']:
        print("**Links:**")
        for i, link in enumerate(data['links'], 1):
            print(f"  {i}. {link['title']}")
            print(f"     URL: {link['url']}")
    else:
        print("**Links:** None")
    
    print("-" * 60)

print(f"\n## Summary")
print(f"Total Sections: {len(sections)}")
print(f"Total Links: {total_links}")

# Save to JSON
with open('/home/<USER>/桌面/sample/final_structure.json', 'w', encoding='utf-8') as f:
    json.dump(sections, f, ensure_ascii=False, indent=2)

print(f"\nComplete structure saved to: /home/<USER>/桌面/sample/final_structure.json")