#!/usr/bin/env python3
import re
import html
import json

def extract_sections_from_html(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find all sections by looking for the pattern
    sections = []
    
    # Pattern to match section divs with their IDs
    section_pattern = r'<div id="([^"]+)" class="section[^"]*">'
    main_section_pattern = r'<div id="([^"]+)" class="section-card">'
    
    # Find all matches
    all_matches = []
    
    # Find main sections (section-card)
    for match in re.finditer(main_section_pattern, content):
        all_matches.append((match.start(), match.group(1), 'main'))
    
    # Find regular sections
    for match in re.finditer(section_pattern, content):
        all_matches.append((match.start(), match.group(1), 'regular'))
    
    # Sort by position
    all_matches.sort()
    
    result = {}
    
    for i, (start_pos, section_id, section_type) in enumerate(all_matches):
        # Find the end position (next section or end of content)
        if i + 1 < len(all_matches):
            end_pos = all_matches[i + 1][0]
        else:
            end_pos = len(content)
        
        section_content = content[start_pos:end_pos]
        
        # Extract title
        title_pattern = r'<h2[^>]*class="[^"]*"[^>]*>([^<]*(?:<[^>]*>[^<]*)*)</h2>'
        title_match = re.search(title_pattern, section_content)
        
        if title_match:
            title_raw = title_match.group(1)
            # Clean up the title - remove HTML tags
            title = re.sub(r'<[^>]*>', '', title_raw).strip()
            title = html.unescape(title)
        else:
            title = section_id
        
        # Extract links
        link_pattern = r'<a\s+href="([^"]+)"[^>]*>([^<]*(?:<[^>]*>[^<]*)*)</a>'
        links = []
        
        for match in re.finditer(link_pattern, section_content):
            url = match.group(1)
            title_text = match.group(2)
            
            # Clean up the link title
            clean_title = re.sub(r'<[^>]*>', '', title_text).strip()
            clean_title = html.unescape(clean_title)
            clean_title = re.sub(r'\s+', ' ', clean_title)  # normalize whitespace
            
            if clean_title and url and not url.startswith('#'):
                links.append({
                    'title': clean_title,
                    'url': url
                })
        
        result[section_id] = {
            'title': title,
            'links': links
        }
    
    return result

# Extract the sections
sections_data = extract_sections_from_html('/home/<USER>/桌面/sample/daohang.html')

# Print the results in a structured format
print("# Complete Structure Analysis of daohang.html")
print("=" * 60)

for section_id, data in sections_data.items():
    print(f"\n## Section ID: {section_id}")
    print(f"**Title:** {data['title']}")
    print(f"**Number of Links:** {len(data['links'])}")
    
    if data['links']:
        print("**Links:**")
        for link in data['links']:
            print(f"  - {link['title']}")
            print(f"    URL: {link['url']}")
    else:
        print("**Links:** None")
    
    print("-" * 40)

# Save to JSON for easier processing
with open('/home/<USER>/桌面/sample/sections_structure.json', 'w', encoding='utf-8') as f:
    json.dump(sections_data, f, ensure_ascii=False, indent=2)

print(f"\nStructure saved to: /home/<USER>/桌面/sample/sections_structure.json")