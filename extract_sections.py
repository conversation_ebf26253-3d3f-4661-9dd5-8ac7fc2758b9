#!/usr/bin/env python3
import re
import html

def extract_sections_from_html(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find all section divs
    section_pattern = r'<div id="([^"]*)" class="section[^"]*">'
    sections = re.findall(section_pattern, content)
    
    # Also find the main section (academicPortal)
    main_section_pattern = r'<div id="([^"]*)" class="section-card">'
    main_sections = re.findall(main_section_pattern, content)
    
    all_sections = main_sections + sections
    
    result = {}
    
    for section_id in all_sections:
        # Find the start and end of this section
        start_pattern = rf'<div id="{section_id}" class="section[^"]*">'
        if section_id in main_sections:
            start_pattern = rf'<div id="{section_id}" class="section-card">'
        
        start_match = re.search(start_pattern, content)
        if not start_match:
            continue
        
        start_pos = start_match.start()
        
        # Find the next section or end of content
        remaining_content = content[start_pos:]
        
        # Find section title
        title_pattern = r'<h2[^>]*>([^<]*(?:<[^>]*>[^<]*)*)</h2>'
        title_match = re.search(title_pattern, remaining_content)
        
        if title_match:
            title = re.sub(r'<[^>]*>', '', title_match.group(1)).strip()
        else:
            title = "Unknown Title"
        
        # Extract all links from this section
        # Look for the next section to know where to stop
        next_section_pattern = r'<div id="[^"]*" class="section'
        next_section_match = re.search(next_section_pattern, remaining_content[100:])  # Skip first 100 chars
        
        if next_section_match:
            section_content = remaining_content[:next_section_match.start() + 100]
        else:
            section_content = remaining_content
        
        # Extract all links
        link_pattern = r'<a href="([^"]*)"[^>]*>([^<]*(?:<[^>]*>[^<]*)*)</a>'
        links = re.findall(link_pattern, section_content)
        
        # Clean up link titles
        cleaned_links = []
        for url, title_text in links:
            clean_title = re.sub(r'<[^>]*>', '', title_text).strip()
            clean_title = html.unescape(clean_title)
            if clean_title and url:
                cleaned_links.append((url, clean_title))
        
        result[section_id] = {
            'title': title,
            'links': cleaned_links
        }
    
    return result

# Extract the sections
sections_data = extract_sections_from_html('/home/<USER>/桌面/sample/daohang.html')

# Print the results
for section_id, data in sections_data.items():
    print(f"\n=== {section_id} ===")
    print(f"Title: {data['title']}")
    print(f"Links: {len(data['links'])}")
    for url, title in data['links']:
        print(f"  - {title}: {url}")