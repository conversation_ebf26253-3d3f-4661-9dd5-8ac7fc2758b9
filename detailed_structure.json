{"academicPortal": {"title": "学术门户", "links": [{"title": "大木虫导航 scihub.net.cn 科学网 iData 中国科技文在线 天津科技大学图书馆 onacademic 小木虫 小木虫科研互动 文献部落 文献小镇 arxiv文献预印 AI赋能科技情报挖掘 aminer 秘塔搜索 员快速考 wolfram搜索 Jquery官网 生信门户网站softberry 生物软件网 chatgpt中文镜像 linux命令查询网站 addgene网站 pytorch官方网站 pytorch安装包 赛特新思 知识星球 medium connectedpapers openknowledgemaps latentbox 东方科软培训 newsnow zeli news.ycombinator.com producthunt deepwiki", "url": "http://4243.net/"}]}, "paperSearch": {"title": "论文搜索与下载", "links": [{"title": "sci-hub(随时更新） sci-hub(国内镜像） 谷歌学术（香港） 谷歌学术（国内） Pubmed 百度学术 Cnpiec LINK service 中国知网 doaj 科研通 科研者之家 Paper digest（论文综述与写作）", "url": "http://4243.net/"}]}, "proteinSites": {"title": "蛋白质网站", "links": [{"title": "RCSB Alphafold Uniprot FASTA PILP PUBchem PROTEINS.PLUS Protein Contacts Atlas pymolwiki showmeai interpro", "url": "https://www.rcsb.org/"}]}, "bookDownload": {"title": "书籍与论文下载网站", "links": [{"title": "BookSC 书香中国 （下载书籍） ProQuest （国外硕博论文）", "url": "https://booksc.xyz/"}]}, "translation": {"title": "翻译网站", "links": [{"title": "deepl 百度翻译 有道翻译", "url": "https://www.deepl.com/translator"}]}, "importantJournals": {"title": "重要论文网址", "links": [{"title": "Bioresouce Technology Bioresouce and Bioengineering Metabolic Engineering Microbial Cell Factories Applied Microbiology and Biotechnology Nature methods Nucleic Acids Research Bioinformatics Briefings in Bioinformatics PLOS Computational Biology Nature Communications Biotechnology Advance current opinion in biotechnology Nature Protocols Frontiers in Microbiology Nature Microbiology And Molecular Biology Reviews Microbiome Nature Microbiology Trends In Microbiology Nature Reviews Microbiology Annual Review Of Microbiology FEMS Microbiology Reviews Applied and Environmental Microbiology Green Chemistry Microbial cell cactories current opinion in biotechnology pnas ACS Catalysis", "url": "https://www.sciencedirect.com/journal/bioresource-technology"}]}, "publicAccounts": {"title": "优质公众号", "links": [{"title": "杜塞说编程 AI超元域", "url": "https://www.dusaiphoto.com/"}]}, "frontendDev": {"title": "前端开发网站", "links": [{"title": "配色网站 uniapp官方网站 Vue官方网站 iconfont图标下载 uni-app数据库常用结构 django官方文档", "url": "https://www.colordrop.io/"}]}, "githubRepos": {"title": "优质github仓库", "links": [{"title": "程序员快速参考：reference CS-Notes 前端技术清单 每天搞定一道前端面试题 build-your-own-x 用git展示神经网络原理 AIsuite open-webui smolagents TEN-Agent Hitomi-Downloader Scrapegraph-ai cobalt browser-use node-deepresearcher crewAI 南哥AGI仓库 阿里核酸大模型 Tyler Reed智能体教程 VideoLingo（语音AI翻译） cursor-ai-downloads openmm gromacs_copilot JinyuanSun", "url": "https://github.com/jaywcjlove/reference"}]}, "molecularDynamics": {"title": "分子动力学模拟", "links": [{"title": "大神Jerkwin的博客网站 蛋白质隧道分析CAVER PredictSNP HotSpot fireprot 蛋白稳定性设计 CASTp Pocket_Finder 能量位点检测Q-SiteFinder 系统发育树Consurf SitesBase ProFunc SiteEngine eF-site PINTS Pymol中文教程 gromacs API 3D卷积神经网络网站 amber官网 MD教程", "url": "http://jerkwin.github.io/"}]}, "bilibiliDownload": {"title": "Bilibili视频下载网站", "links": [{"title": "贝贝Bilibili BilibiliVideoDownload", "url": "https://xbeibeix.com/api/bilibili/"}]}, "patentDownload": {"title": "专利下载软件", "links": [{"title": "润桐 软著查询 国家知识产权局", "url": "https://www.rainpat.com/"}]}, "databases": {"title": "常见数据库汇总", "links": [{"title": "bacdive微生物多样性数据库 中药数据库 ETCM2.0 BATMAN-TCM HERBS ITCM NPASS TCMbank 中医药整合药理学研究平台 酵母基因组数据库 蛋白质热稳定数据 bacdive", "url": "https://bacdive.dsmz.de//"}]}, "programmingEssentials": {"title": "编程必备", "links": [{"title": "python内存动态展示 easyChat工具包 autogen说明书 matplotlib pandas numpy python-graph-gallery（可视化） r-graph-gallery（可视化） d3-graph-gallery（可视化） echarts（可视化） originlab（可视化） pymolwiki pymol中文教程 pymol教育版申请", "url": "https://pythontutor.com/"}]}, "molecularBiology": {"title": "分子生物学工具", "links": [{"title": "UCSF ChimeraX NCBI发育树python包taxoniq NCBI发育树数据库 Motif数据库（interpro） cytoscape（网络可视化） gephi deeploc(人工智能定位) 丹麦技术卫生部系列工具 alphafold3 SwissDock（分子对接） pubchempy（pubchem的python包）", "url": "http://www.cgl.ucsf.edu/chimerax/download.html"}]}, "llmTools": {"title": "大模型工具", "links": [{"title": "ChatGPT Claude Google Bard NVIDIA开发模型 Mistral Ollama Nebius Mistral API 海螺ai音频 海螺AI 斯坦福论文神器 Qwen Deepseek 硅基流动 Le chat Hugging Face GitHub Marketplace 阿里百炼大模型平台 阿里魔搭社区 腾讯元大模型广场 LM Studio 支持本地开发 AnythingLLM tinytroupe(大模型头脑风暴) 火山方舟 chatbox cherrybox gradio ragflow langflow LLaMA-Factory lightning ComfyUI OmniParser2 elicit 科研智能体 TheoremExplainAgent 解释数学定理agent unsloth大模型微调 manus owl n8n csm（声音合成） rllm（编程大模型） sourcegraph cody bolt.new flux-ai图片编辑与生成 vidu.cn视频编辑与生成 res-downloader视频下载 OCRFlux 强大OCR", "url": "https://chat.openai.com/"}]}, "itTools": {"title": "IT工具", "links": [{"title": "IP地址解析 Google IDX APOB-虚拟人合成 Google Whisk BilibiliDown logo生成 Zotero中文社区 bgremove(图片背景去除) Cline Aider Smithery(MCP添加) 字节跳动trae deepgram nova-3(文字转语音工具) 21st.dev(UI设计) universe(UI工具) UV工具（替代pip) autogen langchain MetaGPT deep-research firecrawl sakura-cat（科学上网） obsidian typora napkin(AI绘图) firebase studio（谷歌，浏览器编程） notebooklm（谷歌） copilot uizard durable testim replit ai qodo voiceflow notion ai builder.io LangBot OmniSVG Second-Me KrillinAI 字幕自动生成 prompt优化 万能格式转换aconvert system-prompts-and-models-of-ai-tools biorender生物绘图 lattics 蛋白序列搜索 TangSengDaoDaoServer(即时通讯) 谷歌jules codex 天工智能体 claude code的gui工具 MultiTalk 多人对话 sourcegraph dive-into-llms 动手学大模型", "url": "https://ip138.com"}]}, "mcpTools": {"title": "MCP相关", "links": [{"title": "modelcontextprotocol ai-image-gen-mcp mcp(cursor官网) brave search MCP官方文档 mcpserver glama server cline document mcp servers awesome-mcp-servers awesome mcp server mcp so opensource mcp pulsemcp cursor.directory lmsystems smithery awesome mcp create python server create typescript server cloudflare mcp-worker fastmcp zapier 谷歌A2A refly minimax MCP building code agents with hugging face smolagents rovo-dev-command-line-interface huggingface mcp server", "url": "https://github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking"}]}, "proteinModels": {"title": "蛋白质大模型", "links": [{"title": "AlphaFold RoseTTAFold ESM (Meta) Microsoft Protein Models ProtTrans ProteinLM ProteinMPNN Uni-Fold ProteinLLM awesome-AI-based-protein-design AlphaMissense proteins.sh", "url": "https://github.com/google-deepmind/alphafold"}]}, "aiAgents": {"title": "AI智能体", "links": [{"title": "<PERSON>tGPT Google Bard Perplexity AI Microsoft Copilot Anthropic Pi Poe 天工智能体 minimax agent auto-coder BMAD-METHOD", "url": "https://claude.ai/"}]}}